import { ClickAnimation } from "@/app/page";
import React from "react";
import { FlipCounter } from "@/components/flip-counter";
import { Button } from "@/components/ui/button";
import { Zap } from "lucide-react";
import Image from "next/image";

const Dogs = ({
  handleTeamClick,
  handleBoost,
  scores,
  clickAnimations,
  dogsPercentage,
}: {
  handleTeamClick: any;
  handleBoost: any;
  scores: any;
  clickAnimations: ClickAnimation[];
  dogsPercentage: any;
}) => {
  return (
    <div
      className="flex-1 relative bg-gradient-to-br from-blue-900/50 to-purple-900/50 cursor-pointer select-none overflow-hidden group"
      onClick={(e) => handleTeamClick("dogs", e)}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 group-active:from-blue-500/20 group-active:to-purple-500/20 transition-all duration-150" />

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="text-9xl absolute top-1/4 right-1/4 transform rotate-12">
          🐶
        </div>
        <div className="text-7xl absolute bottom-1/4 left-1/4 transform -rotate-12">
          🐶
        </div>
        <div className="text-6xl absolute top-3/4 right-1/3 transform rotate-6">
          🐶
        </div>
      </div>

      {/* Boost Button - Bottom Right */}
      <div className="absolute bottom-4 right-4 z-20">
        <Button
          onClick={(e) => {
            e.stopPropagation();
            handleBoost("dogs");
          }}
          className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 shadow-lg transform hover:scale-105 transition-all duration-200"
          size="sm"
        >
          <Zap className="w-4 h-4 mr-1" />
          Boost +1K
        </Button>
      </div>

      {/* Main Content */}
      <div className="relative z-10 h-full flex flex-col items-center justify-center p-8">
        {/* Couter */}
        <div className="text-center mb-4">
          <div className="text-3xl sm:text-4xl font-bold text-blue-400 mb-1">
            <FlipCounter value={scores.dogs} />
          </div>
          <div className="text-sm text-blue-300/80">
            ({dogsPercentage.toFixed(1)}%)
          </div>
        </div>
        {/* Couter */}

        <div className="text-8xl sm:text-9xl md:text-[10rem] mb-6 group-active:scale-110 transition-transform duration-150 drop-shadow-2xl">
          <Image
            className="-scale-x-100 p-4"
            src={"/img/dog.webp"}
            alt="dogs"
            width={360}
            height={360}
          />
        </div>
        <h2 className="text-4xl sm:text-5xl md:text-6xl font-bold text-blue-400 mb-4 text-center">
          DOGS
        </h2>
      </div>

      {/* Click Animations */}
      {clickAnimations
        .filter((anim) => anim.team === "dogs")
        .map((anim) => (
          <div
            key={anim.id}
            className="absolute pointer-events-none z-30 text-3xl font-bold text-blue-400 drop-shadow-lg"
            style={{
              left: anim.x - 15,
              top: anim.y - 15,
              animation: "float-up 1s ease-out forwards",
            }}
          >
            +1
          </div>
        ))}
    </div>
  );
};

export default Dogs;
