"use client";

import FlipNumbers from "react-flip-numbers";

interface FlipCounterProps {
  value: number;
  className?: string;
  width?: number;
  height?: number;
  perspective?: number;
}

export function FlipCounter({
  value,
  className = "",
  width = 28,
  height = 40,
  perspective = 1000,
}: FlipCounterProps) {
  const displayStr = value.toLocaleString();
  const paddedDisplay = displayStr.padStart(displayStr.length, " ");

  return (
    <div className={`font-mono ${className}`}>
      <div className="flex">
        {paddedDisplay.split("").map((char, index) => (
          <FlipNumbers
            key={index}
            height={height}
            width={width}
            color="inherit"
            background="transparent"
            play
            perspective={perspective}
            numbers={char.toString()}
          />
        ))}
      </div>
    </div>
  );
}
