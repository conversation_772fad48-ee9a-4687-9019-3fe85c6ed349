import React from "react";
import { FlipCounter } from "@/components/flip-counter";
import { Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ClickAnimation } from "@/app/page";
import Image from "next/image";

const Cats = ({
  handleTeamClick,
  handleBoost,
  scores,
  clickAnimations,
  catsPercentage,
}: {
  handleTeamClick: any;
  handleBoost: any;
  scores: any;
  clickAnimations: ClickAnimation[];
  catsPercentage: any;
}) => {
  return (
    <div
      className="flex-1 relative bg-gradient-to-br from-orange-900/50 to-pink-900/50 cursor-pointer select-none overflow-hidden group"
      onClick={(e) => handleTeamClick("cats", e)}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-pink-500/10 group-active:from-orange-500/20 group-active:to-pink-500/20 transition-all duration-150" />

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="text-9xl absolute top-1/4 left-1/4 transform -rotate-12">
          🐱
        </div>
        <div className="text-7xl absolute bottom-1/4 right-1/4 transform rotate-12">
          🐱
        </div>
        <div className="text-6xl absolute top-3/4 left-1/3 transform -rotate-6">
          🐱
        </div>
      </div>

      {/* Boost Button - Bottom Left */}
      <div className="absolute bottom-4 left-4 z-20">
        <Button
          onClick={(e) => {
            e.stopPropagation();
            handleBoost("cats");
          }}
          className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 shadow-lg transform hover:scale-105 transition-all duration-200"
          size="sm"
        >
          <Zap className="w-4 h-4 mr-1" />
          Boost +1K
        </Button>
      </div>

      {/* Counter at Top */}

      {/* Main Content */}
      <div className="relative z-10 h-full flex flex-col items-center justify-center p-8 ">
        {/* Couter */}

        <div className="text-center mb-4">
          <div className="text-3xl sm:text-4xl font-bold text-orange-400 mb-1">
            <FlipCounter value={scores.cats} />
          </div>
          <div className="text-sm text-orange-300/80">
            ({catsPercentage.toFixed(1)}%)
          </div>
        </div>
        {/* Couter */}

        <div className="text-8xl sm:text-9xl md:text-[10rem] mb-6 group-active:scale-110 transition-transform duration-150 drop-shadow-2xl">
          <Image
            className="p-4"
            src={"/img/cat.webp"}
            alt="cats"
            width={360}
            height={360}
          />
        </div>
        <h2 className="text-4xl sm:text-5xl md:text-6xl font-bold text-orange-400 mb-4 text-center">
          CATS
        </h2>
      </div>

      {/* Click Animations */}
      {clickAnimations
        .filter((anim) => anim.team === "cats")
        .map((anim) => (
          <div
            key={anim.id}
            className="absolute pointer-events-none z-30 text-3xl font-bold text-orange-400 drop-shadow-lg"
            style={{
              left: anim.x - 15,
              top: anim.y - 15,
              animation: "float-up 1s ease-out forwards",
            }}
          >
            +1
          </div>
        ))}
    </div>
  );
};

export default Cats;
